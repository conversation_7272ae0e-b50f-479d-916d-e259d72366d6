report 60080 "Palette Label MXW"
{
    ApplicationArea = All;
    Caption = 'Palette Label';
    UsageCategory = ReportsAndAnalysis;
    DefaultLayout = RDLC;
    RDLCLayout = 'src/reportlayout/PaletteLabelMXW.Label.rdlc';

    dataset
    {
        dataitem(PackageNoInformation; "Package No. Information")
        {
            column(PackageNo; "Package No.")
            {
            }
            column(LocationCodeMXW; "Location Code MXW")
            {
            }
            column(PackageNoBarCode; PackageNoBarCode)
            {
            }
            column(PackageNoQRCode; PackageNoQRCode)
            {
            }
            column(CreatedAtMXW_PackageNoInformation; SystemCreatedAt)
            {
            }
            column(DocumentNoMXW_PackageNoInformation; "Document No. MXW")
            {
            }
            column(ProducedByMXW_PackageNoInformation; "Produced By MXW")
            {
            }

            dataitem("Warehouse Receipt Line Dtl MXW"; "Warehouse Receipt Line Dtl MXW")
            {
                DataItemLink = "Package No." = field("Package No.");

                column(Description_WarehouseReceiptLineDtlMXW; "Item Description")
                {
                }
                column(LineItemNo_WarehouseReceiptLineDtlMXW; "Item No.")
                {
                }
                column(LineVariantCode_WarehouseReceiptLineDtlMXW; "Variant Code")
                {
                }
                column(LotNo_WarehouseReceiptLineDtlMXW; "Lot No.")
                {
                }
                column(Quantity_WarehouseReceiptLineDtlMXW; Quantity)
                {
                }
            }

            trigger OnAfterGetRecord()
            var
                BarcodeFontProvider: Interface "Barcode Font Provider";
                BarcodeFontProvider2D: Interface "Barcode Font Provider 2D";
                BarcodeString: Text;
            begin
                BarcodeFontProvider := Enum::"Barcode Font Provider"::IDAutomation1D;
                BarcodeFontProvider2D := Enum::"Barcode Font Provider 2D"::IDAutomation2D;

                if "Package No." <> '' then begin
                    BarcodeString := "Package No.";

                    BarcodeFontProvider.ValidateInput(BarcodeString, BarcodeSymbology);

                    PackageNoBarCode := BarcodeFontProvider.EncodeFont(BarcodeString, BarcodeSymbology);
                    PackageNoQRCode := BarcodeFontProvider2D.EncodeFont(BarcodeString, BarcodeSymbology2D);
                end
            end;
        }
    }

    trigger OnInitReport()
    begin
        BarcodeSymbology := Enum::"Barcode Symbology"::Code128;
        BarcodeSymbology2D := Enum::"Barcode Symbology 2D"::"QR-Code";
    end;

    var
        BarcodeSymbology: Enum "Barcode Symbology";
        BarcodeSymbology2D: Enum "Barcode Symbology 2D";
        PackageNoBarCode: Text;
        PackageNoQRCode: Text;
}
