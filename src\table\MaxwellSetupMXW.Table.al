table 60000 "Maxwell Setup MXW"
{
    Caption = 'Maxwell Setup';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            Caption = 'Primary Key';
            NotBlank = false;
        }
        // field(2; "Package Nos."; Code[20])
        // {
        //     Caption = 'Package Nos.';
        //     TableRelation = "No. Series".Code;
        //     ToolTip = 'Specifies the number series for package numbers.';
        // }
        // field(3; "Default Warehouse Location"; Code[10])
        // {
        //     Caption = 'Default Warehouse Location';
        //     TableRelation = Location.Code;
        //     ToolTip = 'Specifies the default warehouse location.';
        // }
        // field(4; "Quality Control Required"; Boolean)
        // {
        //     Caption = 'Quality Control Required';
        //     ToolTip = 'Specifies whether quality control is required for warehouse receipts.';
        // }
        // field(5; "Auto Assign Item Tracking"; Boolean)
        // {
        //     Caption = 'Auto Assign Item Tracking';
        //     ToolTip = 'Specifies whether to automatically assign item tracking information.';
        // }
        // field(6; "Quality Control Nos."; Code[20])
        // {
        //     Caption = 'Quality Control Nos.';
        //     TableRelation = "No. Series".Code;
        //     ToolTip = 'Specifies the number series for quality control documents.';
        // }
    }

    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }

    var
        RecordHasBeenRead: Boolean;

    procedure GetRecordOnce()
    begin
        if RecordHasBeenRead then
            exit;
        Get();
        RecordHasBeenRead := true;
    end;

    procedure InsertIfNotExists()
    begin
        Reset();
        if not Get() then begin
            Init();
            Insert(true);
        end;
    end;
}
