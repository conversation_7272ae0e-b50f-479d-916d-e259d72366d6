codeunit 60000 "Maxwell Events MXW"
{
    var
        MaxwellSetup: Record "Maxwell Setup MXW";

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Inbound", OnAfterCreateWhseReceiptHeaderFromWhseRequest, '', false, false)]
    // local procedure "Get Source Doc. Inbound_OnAfterCreateWhseReceiptHeaderFromWhseRequest"(var WhseReceiptHeader: Record "Warehouse Receipt Header"; var WarehouseRequest: Record "Warehouse Request"; var GetSourceDocuments: Report "Get Source Documents")
    // var
    //     PurchaseHeader: Record "Purchase Header";
    //     WarehouseReceiptLine: Record "Warehouse Receipt Line";
    // begin
    //     MaxwellSetup.GetRecordOnce();
    //     WarehouseReceiptLine.SetRange("No.", WhseReceiptHeader."No.");
    //     WarehouseReceiptLine.DeleteQtyToReceive(WarehouseReceiptLine);
    //     WarehouseReceiptLine.Reset();

    //     if WarehouseReceiptLine.FindFirst() then begin
    //         PurchaseHeader.Get(PurchaseHeader."Document Type"::Order, WarehouseReceiptLine."Source No.");
    //         WhseReceiptHeader.Validate("Vendor No. MXW", PurchaseHeader."Pay-to Vendor No.");
    //         WhseReceiptHeader.Modify(true);
    //     end;
    // end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Inbound", OnAfterGetSingleInboundDoc, '', false, false)]
    local procedure "Get Source Doc. Inbound_OnAfterGetSingleInboundDoc"(var WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        PurchaseHeader: Record "Purchase Header";
        WarehouseReceiptLine: Record "Warehouse Receipt Line";
    begin
        MaxwellSetup.GetRecordOnce();
        WarehouseReceiptLine.Reset();
        WarehouseReceiptLine.SetRange("No.", WarehouseReceiptHeader."No.");
        if WarehouseReceiptLine.FindFirst() then begin
            PurchaseHeader.Get(PurchaseHeader."Document Type"::Order, WarehouseReceiptLine."Source No.");
            WarehouseReceiptHeader.Validate("Vendor No. MXW", PurchaseHeader."Pay-to Vendor No.");
            WarehouseReceiptHeader.Modify(true);
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Inbound", OnAfterSetWarehouseRequestFilters, '', false, false)]
    local procedure "Get Source Doc. Inbound_OnAfterSetWarehouseRequestFilters"(var WarehouseRequest: Record "Warehouse Request"; WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    begin
        if WarehouseReceiptHeader."Vendor No. MXW" <> '' then
            WarehouseRequest.SetRange("Destination No.", WarehouseReceiptHeader."Vendor No. MXW");
    end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purchases Warehouse Mgt.", OnPurchLine2ReceiptLineOnAfterSetQtysOnRcptLine, '', false, false)]
    // local procedure "Purchases Warehouse Mgt._OnPurchLine2ReceiptLineOnAfterSetQtysOnRcptLine"(var WarehouseReceiptLine: Record "Warehouse Receipt Line"; PurchaseLine: Record "Purchase Line")
    // begin
    //     WarehouseReceiptLine.Quantity := PurchaseLine."Whse. Rcpt. Qty-to Receive MXW";
    //     WarehouseReceiptLine."Qty. (Base)" := PurchaseLine."Whse. Rcpt. Qty-to Receive MXW";
    //     WarehouseReceiptLine.Validate("Qty. Received", 0);
    //     WarehouseReceiptLine.Validate("Qty. Outstanding", PurchaseLine."Whse. Rcpt. Qty-to Receive MXW");
    //     WarehouseReceiptLine.Validate("Qty. to Receive", 0);

    //     PurchaseLine."Whse. Rcpt. Qty-to Receive MXW" := 0;
    //     PurchaseLine.Modify(true);
    // end;

    // [EventSubscriber(ObjectType::Report, Report::"Get Source Documents", OnAfterPurchaseLineOnPreDataItem, '', false, false)]
    // local procedure "Get Source Documents_OnAfterPurchaseLineOnPreDataItem"(var Sender: Report "Get Source Documents"; var PurchaseLine: Record "Purchase Line"; OneHeaderCreated: Boolean; WhseShptHeader: Record "Warehouse Shipment Header"; WhseReceiptHeader: Record "Warehouse Receipt Header")
    // begin
    //     PurchaseLine.SetFilter("Whse. Rcpt. Qty-to Receive MXW", '>0');
    // end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Receipt", OnAfterCode, '', false, false)]
    local procedure "Whse.-Post Receipt_OnAfterCode"(var WarehouseReceiptHeader: Record "Warehouse Receipt Header"; WarehouseReceiptLine: Record "Warehouse Receipt Line"; CounterSourceDocTotal: Integer; CounterSourceDocOK: Integer)
    begin
        // Post-processing after warehouse receipt posting
        // Can be enhanced with additional business logic as needed
    end;
}
